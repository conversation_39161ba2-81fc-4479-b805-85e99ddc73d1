import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { userOperations } from '@/lib/db';

export default async function DashboardPage() {
  const user = await currentUser();
  
  if (!user) {
    redirect('/sign-in');
  }

  // Get user data from database
  let dbUser;
  try {
    dbUser = await userOperations.findById(user.id);
    
    // If user doesn't exist in database, create them
    if (!dbUser) {
      dbUser = await userOperations.create({
        id: user.id,
        email: user.emailAddresses[0]?.emailAddress || '',
        name: user.fullName || user.firstName || '',
        imageUrl: user.imageUrl,
      });
    }
  } catch (error) {
    console.error('Error fetching/creating user:', error);
    // For now, show a basic dashboard even if DB operations fail
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-8">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Welcome to your Dashboard, {user.firstName || 'User'}!
              </h1>
              <p className="text-lg text-gray-600 mb-6">
                Your AI-powered data extraction workspace
              </p>
              
              {dbUser && (
                <div className="bg-white rounded-lg shadow p-6 mb-6">
                  <h2 className="text-xl font-semibold mb-4">Account Information</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                    <div>
                      <p className="text-sm text-gray-600">Email</p>
                      <p className="font-medium">{dbUser.email}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Credits Available</p>
                      <p className="font-medium text-green-600">{dbUser.creditsAvailable}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Subscription Plan</p>
                      <p className="font-medium">{dbUser.subscriptionPlan}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Member Since</p>
                      <p className="font-medium">{new Date(dbUser.createdAt).toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold mb-2">Upload Documents</h3>
                  <p className="text-gray-600 mb-4">Start extracting data from your documents</p>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Coming Soon
                  </button>
                </div>
                
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold mb-2">View Templates</h3>
                  <p className="text-gray-600 mb-4">Manage your data extraction templates</p>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Coming Soon
                  </button>
                </div>
                
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold mb-2">API Access</h3>
                  <p className="text-gray-600 mb-4">Generate API keys for integration</p>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Coming Soon
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
